import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:pixs/features/home/<USER>/models/collection/collection_response.dart';
import 'package:pixs/features/home/<USER>/models/collection_photo/collection_photo_list_response.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/shared/api/endpoint/api_endpoints.dart';
import 'package:pixs/shared/api/network/network.dart';

import 'package:pixs/shared/utils/result.dart';

import '../../../../shared/api/network/models/open_ai_response.dart';
import '../../../../shared/service/open_ai_service.dart';
import '../repository/home_repository.dart';

@LazySingleton(as: HomeRepository)
class HomeService implements HomeRepository {
  @override
  Future<ResponseResult<List<ImageResponse>>> getImages(
      {required int page}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getImages}?page=$page',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ResponseResult(data: imageResponseFromJson(response.data));
      } else {
        return ResponseResult(error: 'Failed to get products');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<ImageResponse>>> getUserImages(
      {required String username, required int page}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getUserImages(username)}?page=$page',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ResponseResult(data: imageResponseFromJson(response.data));
      } else {
        return ResponseResult(error: 'Failed to get user images');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<CollectionResponse>>> getCollections({required int page}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getCollections}?page=$page&per_page=20',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ResponseResult(data: collectionResponseFromJson(response.data));
      } else {
        return ResponseResult(error: 'Failed to get collections');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<CollectionPhotoListResponse>>> getCollectionPhotos(
      {required int page, required String collectionId}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getCollectionPhotos(collectionId)}?page=$page',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return ResponseResult(
            data: collectionPhotoListResponseFromJson(response.data));
      } else {
        return ResponseResult(error: 'Failed to get collection photos');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<AiResponse>> getImagesByPrompt(
      {required String prompt, required int page}) async {
    try {
      final response = await AiGenerator.i.generate(prompt);
      return ResponseResult(data: response);
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
