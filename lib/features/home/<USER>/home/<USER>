import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:async_wallpaper/async_wallpaper.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pixs/features/home/<USER>/models/collection/collection_response.dart';
import 'package:pixs/features/home/<USER>/models/collection_photo/collection_photo_list_response.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart';
import 'package:pixs/features/home/<USER>/repository/home_repository.dart';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';

import '../../../../shared/api/network/models/open_ai_response.dart';

part 'home_state.dart';

@injectable
class HomeCubit extends Cubit<HomeState> {
  final HomeRepository _homeRepository;
  HomeCubit(this._homeRepository) : super(const HomeState());

  void getImages({int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(imageListFetchStatus: ApiFetchStatus.loading));
    try {
      final result = await _homeRepository.getImages(page: page);
      if (result.data != null) {
        if (isLoadMore) {
          emit(state.copyWith(
            imageListFetchStatus: ApiFetchStatus.success,
            images: [
              ...?state.images,
              ...?result.data,
            ],
          ));
        } else {
          emit(state.copyWith(
            imageListFetchStatus: ApiFetchStatus.success,
            images: result.data,
          ));
        }
      } else {
        emit(state.copyWith(
            imageListFetchStatus: ApiFetchStatus.failed, error: result.error));
      }
    } on Error catch (e) {
      log(e.toString());
      emit(state.copyWith(
          imageListFetchStatus: ApiFetchStatus.failed, error: e.toString()));
    }
  }

  void getUserImages(
      {required String username, int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(userImageListFetchStatus: ApiFetchStatus.loading));
    try {
      final result =
          await _homeRepository.getUserImages(username: username, page: page);
      if (result.data != null) {
        if (isLoadMore) {
          emit(state.copyWith(
            userImageListFetchStatus: ApiFetchStatus.success,
            userImages: [
              ...?state.userImages,
              ...?result.data,
            ],
          ));
        } else {
          emit(state.copyWith(
            userImageListFetchStatus: ApiFetchStatus.success,
            userImages: result.data,
          ));
        }
      } else {
        emit(state.copyWith(
            userImageListFetchStatus: ApiFetchStatus.failed,
            error: result.error));
      }
    } on Error catch (e) {
      log(e.toString());
      emit(state.copyWith(
          userImageListFetchStatus: ApiFetchStatus.failed,
          error: e.toString()));
    }
  }

  void setWallpaper({
    required String imageUrl,
    required String authorName,
    required int option,
  }) async {
    emit(state.copyWith(isWallpaperSetting: true));
    var result = false;
    try {
      result = await AsyncWallpaper.setWallpaper(
        url: imageUrl,
        wallpaperLocation: option,
      )
          ? true
          : false;
    } on PlatformException {}
    log('Wallpaper set from URL result: $result');
    emit(state.copyWith(isWallpaperSetting: false));
  }

  void setWallpaperFromFile({
    required Uint8List imageBytes,
    required int option,
  }) async {
    emit(state.copyWith(isWallpaperSetting: true));
    var result = false;
    try {
      // Save the image bytes to a temporary file
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/edited_wallpaper_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);

      // Set the wallpaper from the file path
      result = await AsyncWallpaper.setWallpaperFromFile(
        filePath: file.path,
        wallpaperLocation: option,
      )
          ? true
          : false;
      log('Wallpaper set from file result: $result');
    } on PlatformException catch (e) {
      log('Error setting wallpaper: ${e.message}');
    } catch (e) {
      log('Error processing image: $e');
    }
    emit(state.copyWith(isWallpaperSetting: false));
  }

  void addToWishList(ImageResponse imageResponse) {
    List<ImageResponse> newWishList = List.from(state.wishListImages ?? []);
    newWishList.insert(0, imageResponse);
    emit(state.copyWith(wishListImages: newWishList));
    saveWishList();
  }

  /// Saves the current wish list to the Hive database.
  void saveWishList() {
    final wishList = state.wishListImages ?? [];
    final value = jsonEncode(wishList);
    var box = Hive.box('wishlist');
    box.put('wishlist', value);
  }

  /// Removes a [ImageResponse] from the wish list.
  ///
  /// @param imageResponse The item to be removed from the wish list.
  void removeFromWishList(ImageResponse imageResponse) {
    List<ImageResponse> newWishList = List.from(state.wishListImages ?? []);
    newWishList.removeWhere((ImageResponse e) => e.id == imageResponse.id);
    emit(state.copyWith(wishListImages: newWishList));
    saveWishList();
  }

  /// Retrieves the wish list from the Hive database and updates the state.
  void getWishList() {
    try {
      var box = Hive.box('wishlist');
      final decodedWishList = jsonDecode(box.get('wishlist')) as List<dynamic>;
      final wishList = decodedWishList
          .map((item) => ImageResponse.fromJson(item as Map<String, dynamic>))
          .toList();
      emit(state.copyWith(wishListImages: wishList));
    } catch (e) {
      log(e.toString());
    }
  }

  /// Clears the wish list from the Hive database and updates the state.
  void clearWishList() {
    var box = Hive.box('wishlist');
    box.delete('wishlist');
    emit(state.copyWith(wishListImages: []));
  }

  void getCollections({int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(collectionListFetchStatus: ApiFetchStatus.loading));

    try {
      final result = await _homeRepository.getCollections(page: page);
      if (result.data != null) {
        if (isLoadMore) {
          emit(state.copyWith(
            collectionListFetchStatus: ApiFetchStatus.success,
            collections: [
              ...?state.collections,
              ...?result.data,
            ],
          ));
        } else {
          emit(state.copyWith(
            collectionListFetchStatus: ApiFetchStatus.success,
            collections: result.data,
          ));
        }
      } else {
        emit(state.copyWith(
            collectionListFetchStatus: ApiFetchStatus.failed,
            error: result.error));
      }
    } on Error catch (e) {
      log(e.toString());
      emit(state.copyWith(
          collectionListFetchStatus: ApiFetchStatus.failed,
          error: e.toString()));
    }
  }
    void updateClickedCardIndex(int index) {
    emit(state.copyWith(clickedCardIndex: index));
  }

  void getCollectionPhotos(
      {required String collectionId, int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(collectionPhotosFetchStatus: ApiFetchStatus.loading));

    log('Getting collection photos for collectionId: $collectionId, page: $page, isLoadMore: $isLoadMore');

    try {
      final result = await _homeRepository.getCollectionPhotos(
          collectionId: collectionId, page: page);
      log('API result: ${result.data?.length} photos, error: ${result.error}');
      if (result.data != null) {
        if (isLoadMore) {
          emit(state.copyWith(
            collectionPhotosFetchStatus: ApiFetchStatus.success,
            collectionPhotos: [
              ...?state.collectionPhotos,
              ...?result.data,
            ],
          ));
        } else {
          emit(state.copyWith(
            collectionPhotosFetchStatus: ApiFetchStatus.success,
            collectionPhotos: result.data,
          ));
        }
      } else {
        emit(state.copyWith(
            collectionPhotosFetchStatus: ApiFetchStatus.failed,
            error: result.error));
      }
    } on Error catch (e) {
      log(e.toString());
      emit(state.copyWith(
          collectionPhotosFetchStatus: ApiFetchStatus.failed,
          error: e.toString()));
    }
  }
  void clearCollectionPhotos() {
    emit(state.copyWith(collectionPhotos: []));
  }
void updatePrompt(String prompt) {
  emit(state.copyWith(prompt: prompt));
}
/// Converts an AiResponseData to an ImageResponse object for display in the ImageDetailsScreen
ImageResponse _convertAiResponseToImageResponse(AiResponseData aiResponseData) {
  // Create a map that matches the structure of ImageResponse
  final Map<String, dynamic> imageResponseMap = {
    'id': DateTime.now().millisecondsSinceEpoch.toString(),
    'description': state.prompt,
    'created_at': DateTime.now().toIso8601String(),
    'urls': {
      'full': aiResponseData.url,
      'regular': aiResponseData.url,
      'small': aiResponseData.url,
      'thumb': aiResponseData.url,
      'raw': aiResponseData.url,
    },
    'user': {
      'name': 'AI Generated',
      'username': 'ai_generated',
      'profile_image': {
        'small': '',
        'medium': '',
        'large': '',
      },
    },
  };

  // Use the fromJson constructor to create the ImageResponse
  return ImageResponse.fromJson(imageResponseMap);
}

void generateImage() async {
  emit(state.copyWith(imageGenerationStatus: ApiFetchStatus.loading));
  try {
    final result = await _homeRepository.getImagesByPrompt(prompt: state.prompt ?? '', page: 1);
    if (result.data != null) {
      emit(state.copyWith(imageGenerationStatus: ApiFetchStatus.success, imageGenerationResult: result.data));
    } else {
      emit(state.copyWith(imageGenerationStatus: ApiFetchStatus.failed, error: result.error));
    }
  } on Error catch (e) {
    log(e.toString());
  }
}

/// Navigates to the ImageDetailsScreen with the generated image
ImageResponse? getGeneratedImageResponse() {
  final aiResponse = state.imageGenerationResult;
  if (aiResponse != null && aiResponse.data != null && aiResponse.data!.isNotEmpty) {
    final firstImage = aiResponse.data!.first;
    if (firstImage.url != null) {
      return _convertAiResponseToImageResponse(firstImage);
    }
  }
  return null;
}
}
