import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pixs/features/category/widgets/category_photo_shimmer.dart';
import 'package:pixs/features/home/<USER>/models/image/image_response.dart' as ImageResponse;
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/widgets/pagination/pagination_widget.dart';

import '../../home/<USER>/models/collection/collection_response.dart';

class CategoryPhotoScreen extends StatelessWidget {
  final CollectionResponse collection;
  const CategoryPhotoScreen({super.key, required this.collection});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        context.read<HomeCubit>().clearCollectionPhotos();
      },
      child: Scaffold(
        appBar: AppBar(),
        body: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            if (state.collectionPhotosFetchStatus == ApiFetchStatus.loading &&
                (state.collectionPhotos?.length ?? 0) == 0) {
              return const CategoryPhotoShimmer();
            }
            if ((state.collectionPhotos?.length ?? 0) == 0) {
              return const Center(child: Text('No photos found'));
            }
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: AnimationLimiter(
                child: PaginationWidget(
                  isPaginating:
                      state.collectionPhotosFetchStatus == ApiFetchStatus.loading,
                  next: (state.collectionPhotos?.length ?? 0) <= 2000,
                  onPagination: (notification) {
                    if (state.collectionPhotos?.length == 2000) {
                      return false;
                    }
                    context.read<HomeCubit>().getCollectionPhotos(
                          collectionId: collection.id ?? '',
                          page: (state.collectionPhotos?.length ?? 0) + 1,
                          isLoadMore: true,
                        );
      
                    return true;
                  },
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 8.0,
                      crossAxisSpacing: 8.0,
                    ),
                    itemCount: state.collectionPhotos?.length ?? 0,
                    itemBuilder: (BuildContext context, int index) =>
                        AnimationConfiguration.staggeredGrid(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      columnCount: 2,
                      child: ScaleAnimation(
                        child: FadeInAnimation(
                          child: GestureDetector(
                            onTap: () => Navigator.pushNamed(
                              context,
                              routeImageDetail,
                              arguments: {
                                'image': ImageResponse.ImageResponse(
                                  urls: ImageResponse.Urls(
                                    small: state.collectionPhotos?[index].urls?.small,
                                    full: state.collectionPhotos?[index].urls?.full,
                                    raw: state.collectionPhotos?[index].urls?.raw,
                                    regular: state.collectionPhotos?[index].urls?.regular,
                                    thumb: state.collectionPhotos?[index].urls?.thumb,
                                  ),
                                  blurHash: state.collectionPhotos?[index].blurHash,
                                  id: state.collectionPhotos?[index].id,
                                  width: state.collectionPhotos?[index].width,
                                  height: state.collectionPhotos?[index].height,
                                  color: state.collectionPhotos?[index].color,
                                  likes: state.collectionPhotos?[index].likes,
                                  likedByUser: state.collectionPhotos?[index].likedByUser,
                                  description: state.collectionPhotos?[index].description,
                                  user: ImageResponse.User(
                                    username: state.collectionPhotos?[index].user?.username,
                                    profileImage: ImageResponse.ProfileImage(
                                      small: state.collectionPhotos?[index].user?.profileImage?.small,
                                      medium: state.collectionPhotos?[index].user?.profileImage?.medium,
                                      large: state.collectionPhotos?[index].user?.profileImage?.large,
                                    ),
                                    bio: state.collectionPhotos?[index].user?.bio,
                                    name: state.collectionPhotos?[index].user?.name,
                                    totalCollections: state.collectionPhotos?[index].user?.totalCollections,
                                    totalLikes: state.collectionPhotos?[index].user?.totalLikes,
                                    totalPhotos: state.collectionPhotos?[index].user?.totalPhotos,
                                    instagramUsername: state.collectionPhotos?[index].user?.instagramUsername,
                                  ),
                                  currentUserCollections: state.collectionPhotos?[index].currentUserCollections?.map((e) => ImageResponse.CurrentUserCollection.fromJson(e.toJson())).toList(),
                                )
                              },
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15.r),
                                color: Colors.black,
      
                                // color: Color((Random().nextDouble() * 0xFFFFFF)
                                //         .toInt())
                                //     .withOpacity(0.2),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15.r),
                                child: CachedNetworkImage(
                                  cacheKey:
                                      state.collectionPhotos?[index].urls?.small,
                                  fit: BoxFit.cover,
                                  imageUrl: state
                                          .collectionPhotos?[index].urls?.small ??
                                      '',
                                  progressIndicatorBuilder:
                                      (context, url, progress) => Opacity(
                                    opacity: 0.5,
                                    child: Center(
                                      child: BlurHash(
                                        hash: state.collectionPhotos?[index]
                                                .blurHash ??
                                            '',
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Center(
                                    child: Opacity(
                                      opacity: 0.5,
                                      child: SizedBox(
                                        width: 50,
                                        child: Center(
                                          child: Image.asset(
                                            Assets.kLogo,
                                            width: 50,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
