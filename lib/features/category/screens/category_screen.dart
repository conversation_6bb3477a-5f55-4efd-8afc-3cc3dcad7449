import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/models/collection/collection_response.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:pixs/shared/widgets/shimmer/shimmer_widget.dart';

class CategoryScreen extends StatelessWidget {
  const CategoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 1000),
          switchInCurve: Curves.easeInOutCubic,
          switchOutCurve: Curves.easeInOutCubic,
          child: state.collectionListFetchStatus == ApiFetchStatus.loading ||
                  state.collectionListFetchStatus == ApiFetchStatus.failed
              ? ListView(
                  key: const ValueKey('loading'),
                  shrinkWrap: true,
                  children: [
                    24.verticalSpace,
                    Center(
                      child: ShimmerWidget(
                        width: 200.w,
                        height: 24.h,
                        isLoading: true,
                      ),
                    ),
                    32.verticalSpace,
                    ListView.builder(
                      key: const ValueKey('loading_list'),
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, index) => Padding(
                        padding: EdgeInsets.only(
                            bottom: 10.0.h, left: 28.w, right: 28.w),
                        child: ShimmerWidget(
                          width: 319.w,
                          height: 146.h,
                          radius: 20,
                          isLoading: true,
                        ),
                      ),
                      itemCount: 3, // Show 5 shimmer items while loading
                    ),
                  ],
                )
              : RefreshIndicator(
                  onRefresh: () async =>
                      context.read<HomeCubit>().getCollections(),
                  child: ListView(
                    physics: const BouncingScrollPhysics(),
                    key: const ValueKey('loaded'),
                    shrinkWrap: true,
                    children: [
                      24.verticalSpace,
                      AnimatedOpacity(
                        opacity: state.collectionListFetchStatus ==
                                ApiFetchStatus.success
                            ? 1.0
                            : 0.0,
                        duration: const Duration(milliseconds: 1200),
                        curve: Curves.easeInOutCubic,
                        child: Center(
                          child: Text(
                            'Discover new collection',
                            style: FontPalette.urbenist20.copyWith(
                              color: kWhite,
                            ),
                          ),
                        ),
                      ),
                      32.verticalSpace,
                      SizedBox(
                        height:
                            (146.h * 0.8) * (state.collections?.length ?? 0) +
                                146.h * 0.2 +
                                350.h,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            ...List.generate(
                              state.collections?.length ?? 0,
                              (index) {
                                bool isSelected =
                                    state.clickedCardIndex == index;
                                return isSelected
                                    ? Container()
                                    : Positioned(
                                        key: ValueKey(
                                            'card_$index'), // Ensure unique key
                                        top: index * (146.h * 0.9),
                                        left: 28,
                                        right: 28,
                                        height: 146.h,
                                        child: AnimatedContainer(
                                          duration: const Duration(
                                              milliseconds: 1200),
                                          curve: Curves.easeInOutCubic,
                                          child: _MagazineCard(
                                            key: ValueKey(
                                                'card_$index'), // Ensure unique key
                                            index: index,
                                            image: state.collections?[index]
                                                    .coverPhoto?.urls?.small ??
                                                '',
                                            collection:
                                                state.collections?[index] ??
                                                    const CollectionResponse(),
                                            isClicked: isSelected,
                                          ),
                                        ),
                                      );
                              },
                            ).reversed,

                            // Render the selected card last to bring it to the front
                            if (state.clickedCardIndex != null)
                              Positioned(
                                key: ValueKey(
                                    'selected_card_${state.clickedCardIndex}'), // Ensure unique key
                                top: state.clickedCardIndex! * (146.h * 0.9),
                                left: 28,
                                right: 28,
                                height: 146.h,
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 1200),
                                  curve: Curves.easeInOutCubic,
                                  transform: Matrix4.identity()
                                    ..translate(0.0, 0.0, 10.0),
                                  child: AnimatedScale(
                                    scale: 1.07,
                                    duration:
                                        const Duration(milliseconds: 1200),
                                    curve: Curves.easeInOutCubic,
                                    child: _MagazineCard(
                                      key: ValueKey(
                                          'selected_card_${state.clickedCardIndex}'),
                                      // Ensure unique key
                                      image: state
                                              .collections?[
                                                  state.clickedCardIndex!]
                                              .coverPhoto
                                              ?.urls
                                              ?.small ??
                                          '',
                                      index: state.clickedCardIndex!,
                                      collection: state.collections?[
                                              state.clickedCardIndex!] ??
                                          const CollectionResponse(),
                                      isClicked: true,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}

class _MagazineCard extends StatelessWidget {
  final String image;
  final bool isClicked;
  final int index;
  final CollectionResponse collection;

  const _MagazineCard({
    super.key,
    required this.image,
    required this.isClicked,
    required this.index,
    required this.collection,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();

        context.read<HomeCubit>().updateClickedCardIndex(index);
      },
      child: Stack(
        children: [
          Container(
            width: 319.w,
            height: 146.h,
            decoration: BoxDecoration(
              color: const Color(0xFF1E1D22),
              borderRadius: BorderRadius.circular(20.r),
              image: DecorationImage(
                image: CachedNetworkImageProvider(image),
                fit: BoxFit.cover,
              ),
              boxShadow: isClicked
                  ? [
                      const BoxShadow(
                        color: kPrimaryColor,
                        blurRadius: 19.0,
                        spreadRadius: 2.0,
                      ),
                    ]
                  : [],
            ),
          ),
          Visibility(
            visible: isClicked,
            child: Positioned(
              bottom: 10,
              right: 16,
              child: AnimatedScale(
                scale: isClicked ? 1.2 : 1.0,
                duration: const Duration(milliseconds: 800),
                curve: Curves.easeInOutCubic,
                child: Material(
                    type: MaterialType
                        .transparency, //Makes it usable on any background color, thanks @IanSmith
                    child: SizedBox(
                      width: 25.w,
                      height: 25.h,
                      child: Ink(
                        decoration: BoxDecoration(
                          border: Border.all(color: kWhite, width: 2.0.w),
                          shape: BoxShape.circle,
                        ),
                        child: InkWell(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            context.read<HomeCubit>().getCollectionPhotos(
                                collectionId: collection.id ?? '');
                            Navigator.pushNamed(context, routeCategoryPhoto,
                                arguments: {'collection': collection});
                          },
                          child: Icon(
                            Icons.arrow_forward_ios_rounded,
                            color: kWhite,
                            size: 20.w,
                          ),
                        ),
                      ),
                    )),
                // ),
              ),
            ),
          ),
          Positioned(
            bottom: 10,
            left: 16,
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: kBlack,
                    blurRadius: 30.0.r,
                    spreadRadius: 0.r,
                  ),
                ],
              ),
              child: Text(
                collection.title ?? '',
                style: FontPalette.urbenist16.copyWith(color: kWhite),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
