import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/home/<USER>/models/collection/collection_response.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/shared/app/enums/api_fetch_status.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:pixs/shared/widgets/shimmer/shimmer_widget.dart';

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({super.key});

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _bounceController;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
    _bounceController.forward();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 1000),
          switchInCurve: Curves.easeInOutCubic,
          switchOutCurve: Curves.easeInOutCubic,
          child: state.collectionListFetchStatus == ApiFetchStatus.loading ||
                  state.collectionListFetchStatus == ApiFetchStatus.failed
              ? ListView(
                  key: const ValueKey('loading'),
                  shrinkWrap: true,
                  children: [
                    24.verticalSpace,
                    Center(
                      child: ShimmerWidget(
                        width: 200.w,
                        height: 24.h,
                        isLoading: true,
                      ),
                    ),
                    32.verticalSpace,
                    ListView.builder(
                      key: const ValueKey('loading_list'),
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, index) => Padding(
                        padding: EdgeInsets.only(
                            bottom: 10.0.h, left: 28.w, right: 28.w),
                        child: ShimmerWidget(
                          width: 319.w,
                          height: 146.h,
                          radius: 20,
                          isLoading: true,
                        ),
                      ),
                      itemCount: 3, // Show 5 shimmer items while loading
                    ),
                  ],
                )
              : RefreshIndicator(
                  onRefresh: () async =>
                      context.read<HomeCubit>().getCollections(),
                  child: CustomScrollView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    slivers: [
                      SliverToBoxAdapter(
                        child: Column(
                          children: [
                            24.verticalSpace,
                            AnimatedBuilder(
                              animation: _bounceAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _bounceAnimation.value,
                                  child: AnimatedOpacity(
                                    opacity: state.collectionListFetchStatus ==
                                            ApiFetchStatus.success
                                        ? 1.0
                                        : 0.0,
                                    duration: const Duration(milliseconds: 1200),
                                    curve: Curves.easeInOutCubic,
                                    child: Center(
                                      child: Text(
                                        'Discover new collection',
                                        style: FontPalette.urbenist20.copyWith(
                                          color: kWhite,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                            32.verticalSpace,
                          ],
                        ),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final collection = state.collections?[index] ??
                                const CollectionResponse();
                            return AnimatedBuilder(
                              animation: _bounceAnimation,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(
                                    0,
                                    (1 - _bounceAnimation.value) * 50 * (index + 1)
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 16.w,
                                      vertical: 8.h,
                                    ),
                                    child: _Enhanced3DCard(
                                      index: index,
                                      image: collection.coverPhoto?.urls?.small ?? '',
                                      collection: collection,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          childCount: state.collections?.length ?? 0,
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}

class _Enhanced3DCard extends StatefulWidget {
  final String image;
  final int index;
  final CollectionResponse collection;

  const _Enhanced3DCard({
    super.key,
    required this.image,
    required this.index,
    required this.collection,
  });

  @override
  State<_Enhanced3DCard> createState() => _Enhanced3DCardState();
}

class _Enhanced3DCardState extends State<_Enhanced3DCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: 8.0,
      end: 16.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _hoverController.forward(),
            onTapUp: (_) => _hoverController.reverse(),
            onTapCancel: () => _hoverController.reverse(),
            onTap: () {
              HapticFeedback.lightImpact();
              context.read<HomeCubit>().getCollectionPhotos(
                  collectionId: widget.collection.id ?? '');
              Navigator.pushNamed(context, routeCategoryPhoto,
                  arguments: {'collection': widget.collection});
            },
            child: Container(
              height: 200.h,
              margin: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: _elevationAnimation.value,
                    spreadRadius: 2.0,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: kPrimaryColor.withOpacity(0.1),
                    blurRadius: _elevationAnimation.value * 0.5,
                    spreadRadius: 1.0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20.r),
                child: Stack(
                  children: [
                    // Background Image
                    Positioned.fill(
                      child: CachedNetworkImage(
                        imageUrl: widget.image,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: const Color(0xFF1E1D22),
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: kPrimaryColor,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: const Color(0xFF1E1D22),
                          child: const Icon(
                            Icons.error,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                    // Gradient Overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.7),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Content
                    Positioned(
                      bottom: 16.h,
                      left: 16.w,
                      right: 16.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.collection.title ?? 'Untitled Collection',
                            style: FontPalette.urbenist18.copyWith(
                              color: kWhite,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          8.verticalSpace,
                          Text(
                            '${widget.collection.totalPhotos ?? 0} photos',
                            style: FontPalette.urbenist14.copyWith(
                              color: kWhite.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Action Button
                    Positioned(
                      top: 16.h,
                      right: 16.w,
                      child: Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: kPrimaryColor.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: [
                            BoxShadow(
                              color: kPrimaryColor.withOpacity(0.3),
                              blurRadius: 8.0,
                              spreadRadius: 1.0,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.arrow_forward_ios_rounded,
                          color: kWhite,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
